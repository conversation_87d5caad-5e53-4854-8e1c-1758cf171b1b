#!/usr/bin/env python3
"""
AI Scraper - A Python CLI tool for scraping product listings using AI.

This tool scrapes product listings from e-commerce websites, extracts structured data
using AI, and saves the results to CSV with downloaded images.
"""
import asyncio
import argparse
import logging
import sys
from datetime import datetime
from typing import List, Dict, Any
from urllib.parse import urlparse
import time

from tqdm.asyncio import tqdm

from config import Config
from scraping.browser import BrowserManager
from scraping.cleaner import HTM<PERSON>leaner
from scraping.extractor import AIExtractor
from scraping.utils import FileManager, ImageDownloader, LoggingSetup, DataValidator

logger = logging.getLogger(__name__)

class ScraperAI:
    """Main scraper class that orchestrates the scraping process."""
    
    def __init__(self):
        self.file_manager = FileManager()
        self.html_cleaner = HTMLCleaner()
        self.ai_extractor = AIExtractor()
        self.data_validator = DataValidator()
        
    async def scrape_products(self, start_url: str = None, max_products: int = None) -> List[Dict[str, Any]]:
        """
        Main scraping method that orchestrates the entire process.
        
        Args:
            start_url: URL to start scraping from
            max_products: Maximum number of products to scrape
            
        Returns:
            List of scraped product data
        """
        start_url = start_url or Config.START_URL
        max_products = max_products or Config.MAX_PRODUCTS
        
        logger.info(f"Starting scrape of {max_products} products from {start_url}")
        
        scraped_products = []
        
        try:
            # Initialize browser
            async with BrowserManager() as browser:
                # Navigate to start URL
                if not await browser.navigate_to_url(start_url):
                    logger.error(f"Failed to navigate to {start_url}")
                    return []
                    
                # Scroll and load more products
                await browser.scroll_and_load_products()
                
                # Extract product URLs
                product_urls = await browser.extract_product_urls()
                
                if not product_urls:
                    logger.error("No product URLs found")
                    return []
                    
                # Limit to max_products
                product_urls = product_urls[:max_products]
                logger.info(f"Found {len(product_urls)} product URLs to scrape")
                
                # Initialize image downloader
                async with ImageDownloader() as img_downloader:
                    # Process each product URL with progress bar
                    for i, product_url in enumerate(tqdm(product_urls, desc="Scraping products")):
                        try:
                            product_data = await self._scrape_single_product(
                                browser, img_downloader, product_url, i + 1
                            )
                            
                            if product_data:
                                scraped_products.append(product_data)
                                logger.info(f"Successfully scraped product {i + 1}: {product_data.get('name', 'Unknown')}")
                            else:
                                logger.warning(f"Failed to scrape product {i + 1}: {product_url}")
                                
                            # Add delay between requests
                            if i < len(product_urls) - 1:
                                await asyncio.sleep(Config.REQUEST_DELAY)
                                
                        except Exception as e:
                            logger.error(f"Error scraping product {i + 1} ({product_url}): {e}")
                            continue
                            
        except Exception as e:
            logger.error(f"Error in main scraping process: {e}")
            
        logger.info(f"Scraping completed. Successfully scraped {len(scraped_products)} products")
        return scraped_products
        
    async def _scrape_single_product(
        self, 
        browser: BrowserManager, 
        img_downloader: ImageDownloader, 
        product_url: str, 
        product_index: int
    ) -> Dict[str, Any]:
        """Scrape a single product page."""
        try:
            # Navigate to product page
            if not await browser.navigate_to_url(product_url):
                logger.warning(f"Failed to navigate to product: {product_url}")
                return None
                
            # Get page HTML
            html_content = await browser.get_page_html()
            if not html_content:
                logger.warning(f"No HTML content for product: {product_url}")
                return None
                
            # Save raw HTML if configured
            if Config.SAVE_RAW_HTML:
                filename = f"product_{product_index:03d}"
                self.file_manager.save_html(html_content, filename)
                
            # Clean HTML
            cleaned_html = self.html_cleaner.clean_html(html_content)
            
            # Extract basic info as fallback
            fallback_data = self.html_cleaner.extract_product_info(html_content)
            
            # Use AI to extract structured data
            extracted_data = await self.ai_extractor.extract_product_data(cleaned_html, fallback_data)
            
            # Add metadata
            extracted_data['source_url'] = product_url
            extracted_data['scraped_at'] = datetime.now().isoformat()
            
            # Validate and clean data
            validated_data = self.data_validator.validate_product_data(extracted_data)
            
            # Download image if available
            if validated_data.get('image_url'):
                image_filename = self.file_manager.generate_filename(
                    validated_data.get('name', f'product_{product_index}'),
                    'vendor'  # Could extract vendor from URL or data
                )
                
                local_image_path = await img_downloader.download_image(
                    validated_data['image_url'],
                    image_filename,
                    product_url
                )
                
                if local_image_path:
                    validated_data['local_image_path'] = local_image_path
                    
            # Calculate quality score
            quality_score = self.data_validator.calculate_completeness_score(validated_data)
            validated_data['quality_score'] = quality_score
            
            logger.debug(f"Product scraped with quality score: {quality_score:.2f}")
            
            return validated_data
            
        except Exception as e:
            logger.error(f"Error scraping single product {product_url}: {e}")
            return None
            
    def save_results(self, products_data: List[Dict[str, Any]]) -> bool:
        """Save scraped results to CSV file."""
        try:
            if not products_data:
                logger.warning("No products data to save")
                return False
                
            # Save to CSV
            success = self.file_manager.save_to_csv(products_data)
            
            if success:
                logger.info(f"Results saved to {Config.OUTPUT_CSV_PATH}")
                
                # Print summary statistics
                total_products = len(products_data)
                products_with_images = sum(1 for p in products_data if p.get('local_image_path'))
                avg_quality = sum(p.get('quality_score', 0) for p in products_data) / total_products if total_products > 0 else 0
                
                logger.info(f"Summary: {total_products} products, {products_with_images} with images, avg quality: {avg_quality:.2f}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
            return False

def create_argument_parser() -> argparse.ArgumentParser:
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        description="AI Scraper - Scrape product listings using AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                                    # Use default settings
  python main.py --url "https://jiji.co.tz/cars"   # Scrape cars instead
  python main.py --max-products 20                 # Scrape 20 products
  python main.py --debug                           # Enable debug logging
        """
    )
    
    parser.add_argument(
        '--url',
        type=str,
        help=f'Start URL to scrape (default: {Config.START_URL})'
    )
    
    parser.add_argument(
        '--max-products',
        type=int,
        help=f'Maximum number of products to scrape (default: {Config.MAX_PRODUCTS})'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        help=f'Output CSV file path (default: {Config.OUTPUT_CSV_PATH})'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug logging'
    )
    
    parser.add_argument(
        '--headless',
        type=bool,
        default=Config.HEADLESS,
        help='Run browser in headless mode (default: True)'
    )
    
    return parser

async def main():
    """Main entry point for the CLI application."""
    # Parse command line arguments
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Override config with command line arguments
    if args.url:
        Config.START_URL = args.url
    if args.max_products:
        Config.MAX_PRODUCTS = args.max_products
    if args.output:
        Config.OUTPUT_CSV_PATH = args.output
    if args.debug:
        Config.DEBUG = True
    if hasattr(args, 'headless'):
        Config.HEADLESS = args.headless
        
    # Setup logging
    LoggingSetup.setup_logging()
    
    # Validate configuration
    try:
        Config.validate()
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        sys.exit(1)
        
    logger.info("AI Scraper starting...")
    logger.info(f"Target URL: {Config.START_URL}")
    logger.info(f"Max products: {Config.MAX_PRODUCTS}")
    logger.info(f"Output file: {Config.OUTPUT_CSV_PATH}")
    
    # Initialize scraper
    scraper = ScraperAI()
    
    try:
        start_time = time.time()
        
        # Run scraping process
        products_data = await scraper.scrape_products(
            start_url=Config.START_URL,
            max_products=Config.MAX_PRODUCTS
        )
        
        # Save results
        if products_data:
            success = scraper.save_results(products_data)
            if success:
                elapsed_time = time.time() - start_time
                logger.info(f"Scraping completed successfully in {elapsed_time:.2f} seconds")
                logger.info(f"Results saved to: {Config.OUTPUT_CSV_PATH}")
            else:
                logger.error("Failed to save results")
                sys.exit(1)
        else:
            logger.error("No products were scraped")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
