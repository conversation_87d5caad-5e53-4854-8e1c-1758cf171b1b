2025-08-04 18:14:47 - scraping.utils - INFO - Logging configured successfully
2025-08-04 18:14:47 - __main__ - INFO - AI Scraper starting...
2025-08-04 18:14:47 - __main__ - INFO - Target URL: https://jiji.co.tz/mobile-phones
2025-08-04 18:14:47 - __main__ - INFO - Max products: 10
2025-08-04 18:14:47 - __main__ - INFO - Output file: data/output.csv
2025-08-04 18:14:47 - __main__ - INFO - Starting scrape of 10 products from https://jiji.co.tz/mobile-phones
2025-08-04 18:15:07 - scraping.browser - INFO - Browser initialized successfully
2025-08-04 18:15:07 - scraping.browser - INFO - Navigating to: https://jiji.co.tz/mobile-phones (attempt 1)
2025-08-04 18:15:41 - scraping.browser - ERROR - Navigation attempt 1 failed for https://jiji.co.tz/mobile-phones: Timeout 10000ms exceeded.
=========================== logs ===========================
"load" event fired
============================================================
2025-08-04 18:15:42 - scraping.browser - INFO - Navigating to: https://jiji.co.tz/mobile-phones (attempt 2)
2025-08-04 18:15:58 - scraping.browser - ERROR - Navigation attempt 2 failed for https://jiji.co.tz/mobile-phones: Timeout 10000ms exceeded.
=========================== logs ===========================
"load" event fired
============================================================
2025-08-04 18:16:00 - scraping.browser - INFO - Navigating to: https://jiji.co.tz/mobile-phones (attempt 3)
2025-08-04 18:16:09 - scraping.browser - INFO - Scrolling attempt 1/3
2025-08-04 18:16:12 - scraping.browser - INFO - Scrolling attempt 2/3
2025-08-04 18:16:14 - scraping.browser - INFO - Scrolling attempt 3/3
2025-08-04 18:16:16 - scraping.browser - ERROR - Error extracting product URLs: object str can't be used in 'await' expression
2025-08-04 18:16:16 - __main__ - ERROR - No product URLs found
2025-08-04 18:16:16 - scraping.browser - INFO - Browser closed successfully
2025-08-04 18:16:16 - __main__ - ERROR - No products were scraped
