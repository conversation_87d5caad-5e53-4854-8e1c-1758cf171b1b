"""
HTML cleaning and processing module using BeautifulSoup.
"""
import logging
import re
from typing import Optional, Dict, Any
from bs4 import BeautifulSoup, Comment, NavigableString

logger = logging.getLogger(__name__)

class HTMLCleaner:
    """Cleans and processes HTML content for AI extraction."""
    
    def __init__(self):
        self.unwanted_tags = {
            'script', 'style', 'noscript', 'iframe', 'embed', 'object',
            'applet', 'form', 'input', 'button', 'select', 'textarea',
            'nav', 'header', 'footer', 'aside', 'menu', 'menuitem'
        }
        
        self.unwanted_classes = {
            'advertisement', 'ads', 'ad-banner', 'popup', 'modal',
            'cookie', 'newsletter', 'subscription', 'social-share',
            'related-products', 'recommendations', 'sidebar',
            'navigation', 'breadcrumb', 'pagination', 'footer',
            'header', 'menu', 'search-bar', 'filters'
        }
        
        self.unwanted_ids = {
            'advertisement', 'ads', 'popup', 'modal', 'cookie-banner',
            'newsletter', 'social-media', 'sidebar', 'navigation',
            'header', 'footer', 'menu', 'search', 'filters'
        }
        
    def clean_html(self, html_content: str, preserve_structure: bool = True) -> str:
        """
        Clean HTML content by removing unwanted elements and scripts.
        
        Args:
            html_content: Raw HTML content
            preserve_structure: Whether to preserve basic HTML structure
            
        Returns:
            Cleaned HTML content
        """
        try:
            if not html_content or not html_content.strip():
                logger.warning("Empty HTML content provided")
                return ""
                
            # Parse HTML with BeautifulSoup
            soup = BeautifulSoup(html_content, 'lxml')
            
            # Remove unwanted tags
            self._remove_unwanted_tags(soup)
            
            # Remove unwanted elements by class and id
            self._remove_unwanted_elements(soup)
            
            # Remove comments
            self._remove_comments(soup)
            
            # Clean attributes
            self._clean_attributes(soup)
            
            # Remove empty elements
            self._remove_empty_elements(soup)
            
            # Extract and clean text content
            if preserve_structure:
                cleaned_html = str(soup)
            else:
                cleaned_html = self._extract_text_content(soup)
                
            # Final text cleaning
            cleaned_html = self._clean_text(cleaned_html)
            
            logger.debug(f"HTML cleaned successfully. Original: {len(html_content)} chars, Cleaned: {len(cleaned_html)} chars")
            return cleaned_html
            
        except Exception as e:
            logger.error(f"Error cleaning HTML: {e}")
            return html_content  # Return original if cleaning fails
            
    def _remove_unwanted_tags(self, soup: BeautifulSoup) -> None:
        """Remove unwanted HTML tags."""
        for tag_name in self.unwanted_tags:
            for tag in soup.find_all(tag_name):
                tag.decompose()
                
    def _remove_unwanted_elements(self, soup: BeautifulSoup) -> None:
        """Remove elements with unwanted classes or IDs."""
        # Remove by class
        for class_name in self.unwanted_classes:
            for element in soup.find_all(class_=lambda x: x and class_name in ' '.join(x).lower()):
                element.decompose()
                
        # Remove by ID
        for id_name in self.unwanted_ids:
            for element in soup.find_all(id=lambda x: x and id_name in x.lower()):
                element.decompose()
                
        # Remove elements with specific attributes
        unwanted_attrs = [
            {'data-testid': re.compile(r'(ad|advertisement|popup|modal)', re.I)},
            {'role': re.compile(r'(banner|navigation|complementary)', re.I)},
            {'aria-label': re.compile(r'(advertisement|ad|popup)', re.I)}
        ]
        
        for attr_dict in unwanted_attrs:
            for attr_name, pattern in attr_dict.items():
                for element in soup.find_all(attrs={attr_name: pattern}):
                    element.decompose()
                    
    def _remove_comments(self, soup: BeautifulSoup) -> None:
        """Remove HTML comments."""
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()
            
    def _clean_attributes(self, soup: BeautifulSoup) -> None:
        """Clean and remove unnecessary attributes."""
        # Attributes to keep for structure and content identification
        keep_attrs = {
            'class', 'id', 'data-price', 'data-title', 'data-description',
            'alt', 'title', 'href', 'src', 'data-src', 'itemprop',
            'itemtype', 'itemscope'
        }
        
        for tag in soup.find_all():
            if tag.attrs:
                # Keep only useful attributes
                attrs_to_remove = []
                for attr in tag.attrs:
                    if attr not in keep_attrs and not attr.startswith('data-'):
                        attrs_to_remove.append(attr)
                        
                for attr in attrs_to_remove:
                    del tag.attrs[attr]
                    
    def _remove_empty_elements(self, soup: BeautifulSoup) -> None:
        """Remove empty elements that don't contribute to content."""
        # Tags that should be removed if empty
        removable_if_empty = {
            'div', 'span', 'p', 'section', 'article', 'aside',
            'header', 'footer', 'main', 'figure', 'figcaption'
        }
        
        # Multiple passes to handle nested empty elements
        for _ in range(3):
            for tag_name in removable_if_empty:
                for tag in soup.find_all(tag_name):
                    if not tag.get_text(strip=True) and not tag.find('img'):
                        tag.decompose()
                        
    def _extract_text_content(self, soup: BeautifulSoup) -> str:
        """Extract clean text content from soup."""
        # Focus on content-rich elements
        content_selectors = [
            '[itemprop="name"]',
            '[itemprop="price"]',
            '[itemprop="description"]',
            '.product-title',
            '.product-name',
            '.product-price',
            '.product-description',
            '.price',
            '.title',
            '.description',
            'h1', 'h2', 'h3',
            '.content',
            '.details',
            '.specifications'
        ]
        
        extracted_parts = []
        
        for selector in content_selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                if text and len(text) > 3:  # Avoid very short meaningless text
                    extracted_parts.append(text)
                    
        # If no specific content found, extract all text
        if not extracted_parts:
            extracted_parts.append(soup.get_text(separator=' ', strip=True))
            
        return '\n'.join(extracted_parts)
        
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
            
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove common unwanted patterns
        unwanted_patterns = [
            r'(?i)cookie\s+policy',
            r'(?i)privacy\s+policy',
            r'(?i)terms\s+of\s+service',
            r'(?i)subscribe\s+to\s+newsletter',
            r'(?i)follow\s+us\s+on',
            r'(?i)share\s+this',
            r'(?i)related\s+products',
            r'(?i)you\s+may\s+also\s+like',
            r'(?i)advertisement',
            r'(?i)sponsored',
        ]
        
        for pattern in unwanted_patterns:
            text = re.sub(pattern, '', text)
            
        # Clean up multiple spaces and newlines
        text = re.sub(r'\n\s*\n', '\n', text)
        text = re.sub(r' +', ' ', text)
        
        return text.strip()
        
    def extract_product_info(self, html_content: str) -> Dict[str, Any]:
        """
        Extract basic product information using common patterns.
        This serves as a fallback before AI extraction.
        """
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            
            product_info = {
                'name': '',
                'price': '',
                'description': '',
                'image_url': ''
            }
            
            # Extract name
            name_selectors = [
                '[itemprop="name"]',
                '.product-title',
                '.product-name',
                '.listing-title',
                'h1',
                '.title'
            ]
            
            for selector in name_selectors:
                element = soup.select_one(selector)
                if element:
                    product_info['name'] = element.get_text(strip=True)
                    break
                    
            # Extract price
            price_selectors = [
                '[itemprop="price"]',
                '.product-price',
                '.price',
                '[data-price]',
                '.amount'
            ]
            
            for selector in price_selectors:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    # Extract numeric price
                    price_match = re.search(r'[\d,]+(?:\.\d{2})?', price_text)
                    if price_match:
                        product_info['price'] = price_match.group()
                        break
                        
            # Extract description
            desc_selectors = [
                '[itemprop="description"]',
                '.product-description',
                '.description',
                '.details',
                '.content'
            ]
            
            for selector in desc_selectors:
                element = soup.select_one(selector)
                if element:
                    desc_text = element.get_text(strip=True)
                    if len(desc_text) > 20:  # Ensure meaningful description
                        product_info['description'] = desc_text[:500]  # Limit length
                        break
                        
            # Extract image URL
            img_selectors = [
                '[itemprop="image"]',
                '.product-image img',
                '.main-image img',
                '.gallery img:first-child',
                'img[alt*="product"]',
                'img[src*="product"]'
            ]
            
            for selector in img_selectors:
                element = soup.select_one(selector)
                if element:
                    img_url = element.get('src') or element.get('data-src')
                    if img_url:
                        product_info['image_url'] = img_url
                        break
                        
            return product_info
            
        except Exception as e:
            logger.error(f"Error extracting product info: {e}")
            return {'name': '', 'price': '', 'description': '', 'image_url': ''}
