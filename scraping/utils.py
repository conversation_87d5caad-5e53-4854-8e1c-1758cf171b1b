"""
Utility functions for file operations, logging, and data processing.
"""
import os
import csv
import logging
import asyncio
import aiohttp
from pathlib import Path
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse, urljoin
import re
from PIL import Image
import io

from config import Config

logger = logging.getLogger(__name__)

class FileManager:
    """Manages file operations for the scraper."""
    
    def __init__(self):
        self.ensure_directories()
        
    def ensure_directories(self):
        """Ensure all required directories exist."""
        directories = [
            Config.RAW_HTML_DIR,
            Config.IMAGES_DIR,
            os.path.dirname(Config.LOG_FILE),
            os.path.dirname(Config.OUTPUT_CSV_PATH)
        ]
        
        for directory in directories:
            if directory:
                Path(directory).mkdir(parents=True, exist_ok=True)
                
    def save_html(self, html_content: str, filename: str) -> bool:
        """Save HTML content to file."""
        try:
            if not Config.SAVE_RAW_HTML:
                return True
                
            filepath = Path(Config.RAW_HTML_DIR) / f"{filename}.html"
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
            logger.debug(f"HTML saved to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save HTML to {filename}: {e}")
            return False
            
    def save_to_csv(self, products_data: List[Dict[str, Any]], append: bool = False) -> bool:
        """Save product data to CSV file."""
        try:
            if not products_data:
                logger.warning("No product data to save")
                return False
                
            filepath = Path(Config.OUTPUT_CSV_PATH)
            mode = 'a' if append and filepath.exists() else 'w'
            
            # Define CSV headers
            headers = ['name', 'price', 'description', 'image_url', 'source_url', 'scraped_at']
            
            with open(filepath, mode, newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=headers)
                
                # Write header only for new files
                if mode == 'w':
                    writer.writeheader()
                    
                # Write product data
                for product in products_data:
                    # Ensure all required fields exist
                    row = {header: product.get(header, '') for header in headers}
                    writer.writerow(row)
                    
            logger.info(f"Saved {len(products_data)} products to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save CSV: {e}")
            return False
            
    def generate_filename(self, product_name: str, vendor: str = "", extension: str = "jpg") -> str:
        """Generate a clean filename for images."""
        # Clean product name
        clean_name = re.sub(r'[^\w\s-]', '', product_name.lower())
        clean_name = re.sub(r'[-\s]+', '_', clean_name)
        clean_name = clean_name[:50]  # Limit length
        
        # Clean vendor name
        clean_vendor = ""
        if vendor:
            clean_vendor = re.sub(r'[^\w\s-]', '', vendor.lower())
            clean_vendor = re.sub(r'[-\s]+', '_', clean_vendor)
            clean_vendor = clean_vendor[:20]
            
        # Combine parts
        if clean_vendor:
            filename = f"{clean_name}_{clean_vendor}.{extension}"
        else:
            filename = f"{clean_name}.{extension}"
            
        return filename

class ImageDownloader:
    """Handles image downloading and processing."""
    
    def __init__(self):
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'User-Agent': Config.USER_AGENT}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
            
    async def download_image(self, image_url: str, filename: str, base_url: str = "") -> Optional[str]:
        """
        Download image from URL and save to local file.
        
        Args:
            image_url: URL of the image to download
            filename: Local filename to save the image
            base_url: Base URL for resolving relative URLs
            
        Returns:
            Local file path if successful, None otherwise
        """
        try:
            if not image_url:
                return None
                
            # Resolve relative URLs
            if image_url.startswith('//'):
                image_url = 'https:' + image_url
            elif image_url.startswith('/'):
                if base_url:
                    parsed_base = urlparse(base_url)
                    image_url = f"{parsed_base.scheme}://{parsed_base.netloc}{image_url}"
                else:
                    logger.warning(f"Cannot resolve relative URL without base: {image_url}")
                    return None
            elif not image_url.startswith(('http://', 'https://')):
                if base_url:
                    image_url = urljoin(base_url, image_url)
                else:
                    logger.warning(f"Invalid image URL: {image_url}")
                    return None
                    
            # Download image
            async with self.session.get(image_url) as response:
                if response.status == 200:
                    content = await response.read()
                    
                    # Validate and process image
                    processed_content = self._process_image(content)
                    if processed_content:
                        # Save to file
                        filepath = Path(Config.IMAGES_DIR) / filename
                        
                        with open(filepath, 'wb') as f:
                            f.write(processed_content)
                            
                        logger.debug(f"Image downloaded: {image_url} -> {filepath}")
                        return str(filepath)
                    else:
                        logger.warning(f"Invalid image content from {image_url}")
                        return None
                else:
                    logger.warning(f"Failed to download image {image_url}: HTTP {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error downloading image {image_url}: {e}")
            return None
            
    def _process_image(self, image_content: bytes) -> Optional[bytes]:
        """Process and validate image content."""
        try:
            # Validate image using PIL
            image = Image.open(io.BytesIO(image_content))
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'P'):
                image = image.convert('RGB')
                
            # Resize if too large (optional optimization)
            max_size = (1200, 1200)
            if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # Save processed image to bytes
                output = io.BytesIO()
                image.save(output, format='JPEG', quality=85, optimize=True)
                return output.getvalue()
            else:
                # Return original content if no processing needed
                return image_content
                
        except Exception as e:
            logger.error(f"Error processing image: {e}")
            return None

class LoggingSetup:
    """Sets up logging configuration for the scraper."""
    
    @staticmethod
    def setup_logging():
        """Configure logging for the application."""
        # Ensure log directory exists
        log_dir = os.path.dirname(Config.LOG_FILE)
        if log_dir:
            Path(log_dir).mkdir(parents=True, exist_ok=True)
            
        # Configure logging format
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        date_format = '%Y-%m-%d %H:%M:%S'
        
        # Set up file handler
        file_handler = logging.FileHandler(Config.LOG_FILE, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG if Config.DEBUG else logging.INFO)
        file_handler.setFormatter(logging.Formatter(log_format, date_format))
        
        # Set up console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(logging.Formatter(log_format, date_format))
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG if Config.DEBUG else logging.INFO)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        
        # Reduce noise from external libraries
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('aiohttp').setLevel(logging.WARNING)
        logging.getLogger('playwright').setLevel(logging.WARNING)
        
        logger.info("Logging configured successfully")

class DataValidator:
    """Validates and cleans scraped data."""
    
    @staticmethod
    def validate_product_data(product: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean product data."""
        validated = {
            'name': '',
            'price': '',
            'description': '',
            'image_url': '',
            'source_url': '',
            'scraped_at': ''
        }
        
        # Validate name
        if product.get('name'):
            name = str(product['name']).strip()
            if len(name) > 3:
                validated['name'] = name[:200]
                
        # Validate price
        if product.get('price'):
            price = str(product['price']).strip()
            # Extract numeric value
            price_match = re.search(r'[\d,]+(?:\.\d{2})?', price)
            if price_match:
                validated['price'] = price_match.group().replace(',', '')
                
        # Validate description
        if product.get('description'):
            desc = str(product['description']).strip()
            if len(desc) > 10:
                validated['description'] = desc[:500]
                
        # Validate image URL
        if product.get('image_url'):
            img_url = str(product['image_url']).strip()
            if img_url and (img_url.startswith(('http', '//')) or img_url.startswith('/')):
                validated['image_url'] = img_url
                
        # Copy other fields
        for field in ['source_url', 'scraped_at']:
            if product.get(field):
                validated[field] = str(product[field]).strip()
                
        return validated
        
    @staticmethod
    def calculate_completeness_score(product: Dict[str, Any]) -> float:
        """Calculate completeness score for product data."""
        score = 0.0
        weights = {
            'name': 0.4,
            'price': 0.3,
            'description': 0.2,
            'image_url': 0.1
        }
        
        for field, weight in weights.items():
            if product.get(field) and len(str(product[field]).strip()) > 0:
                score += weight
                
        return score
