"""
Browser automation module using <PERSON><PERSON> for web scraping.
"""
import asyncio
import logging
import time
from typing import List, Optional, Dict, Any
from urllib.parse import urljoin, urlparse

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
from config import Config

logger = logging.getLogger(__name__)

class BrowserManager:
    """Manages browser automation for scraping product listings."""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
        
    async def start(self):
        """Initialize browser and create context."""
        try:
            self.playwright = await async_playwright().start()
            
            # Launch browser
            self.browser = await self.playwright.chromium.launch(
                headless=Config.HEADLESS,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-extensions',
                ]
            )
            
            # Create context with realistic settings
            self.context = await self.browser.new_context(
                user_agent=Config.USER_AGENT,
                viewport={'width': 1920, 'height': 1080},
                extra_http_headers={
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                }
            )
            
            # Set timeouts
            self.context.set_default_timeout(Config.BROWSER_TIMEOUT)
            self.context.set_default_navigation_timeout(Config.PAGE_LOAD_TIMEOUT)
            
            # Create page
            self.page = await self.context.new_page()
            
            logger.info("Browser initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            await self.close()
            raise
            
    async def close(self):
        """Close browser and cleanup resources."""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
                
            logger.info("Browser closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing browser: {e}")
            
    async def navigate_to_url(self, url: str) -> bool:
        """Navigate to a URL with retry logic."""
        for attempt in range(Config.RETRY_ATTEMPTS):
            try:
                logger.info(f"Navigating to: {url} (attempt {attempt + 1})")
                
                response = await self.page.goto(url, wait_until='domcontentloaded')
                
                if response and response.status < 400:
                    # Wait for page to be fully loaded
                    await self.page.wait_for_load_state('networkidle', timeout=10000)
                    await asyncio.sleep(Config.REQUEST_DELAY)
                    return True
                else:
                    logger.warning(f"HTTP {response.status if response else 'No response'} for {url}")
                    
            except Exception as e:
                logger.error(f"Navigation attempt {attempt + 1} failed for {url}: {e}")
                if attempt < Config.RETRY_ATTEMPTS - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    
        return False
        
    async def scroll_and_load_products(self) -> None:
        """Scroll page to load more products dynamically."""
        try:
            for i in range(Config.SCROLL_ATTEMPTS):
                logger.info(f"Scrolling attempt {i + 1}/{Config.SCROLL_ATTEMPTS}")
                
                # Scroll to bottom
                await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await asyncio.sleep(2)
                
                # Check if "Load More" button exists and click it
                load_more_selectors = [
                    'button:has-text("Load More")',
                    'button:has-text("Show More")',
                    '.load-more',
                    '[data-testid="load-more"]'
                ]
                
                for selector in load_more_selectors:
                    try:
                        if await self.page.is_visible(selector):
                            await self.page.click(selector)
                            await asyncio.sleep(3)
                            logger.info(f"Clicked load more button: {selector}")
                            break
                    except:
                        continue
                        
        except Exception as e:
            logger.error(f"Error during scrolling: {e}")
            
    async def extract_product_urls(self) -> List[str]:
        """Extract product URLs from the current page."""
        try:
            # Common selectors for product links on e-commerce sites
            selectors = [
                'a[href*="/ad/"]',  # Jiji specific
                'a[href*="/product/"]',
                'a[href*="/item/"]',
                'a[href*="/listing/"]',
                '.product-item a',
                '.listing-item a',
                '.ad-item a',
                '[data-testid="product-link"]'
            ]
            
            product_urls = set()
            base_url = await self.page.url
            parsed_base = urlparse(base_url)
            base_domain = f"{parsed_base.scheme}://{parsed_base.netloc}"
            
            for selector in selectors:
                try:
                    links = await self.page.query_selector_all(selector)
                    for link in links:
                        href = await link.get_attribute('href')
                        if href:
                            # Convert relative URLs to absolute
                            if href.startswith('/'):
                                full_url = urljoin(base_domain, href)
                            elif href.startswith('http'):
                                full_url = href
                            else:
                                full_url = urljoin(base_url, href)
                                
                            # Filter out non-product URLs
                            if self._is_product_url(full_url):
                                product_urls.add(full_url)
                                
                except Exception as e:
                    logger.debug(f"Error with selector {selector}: {e}")
                    continue
                    
            urls_list = list(product_urls)[:Config.MAX_PRODUCTS]
            logger.info(f"Found {len(urls_list)} product URLs")
            return urls_list
            
        except Exception as e:
            logger.error(f"Error extracting product URLs: {e}")
            return []
            
    def _is_product_url(self, url: str) -> bool:
        """Check if URL is likely a product page."""
        product_indicators = [
            '/ad/', '/product/', '/item/', '/listing/',
            '/mobile-phone', '/phone', '/smartphone'
        ]
        
        # Exclude common non-product pages
        exclude_patterns = [
            '/search', '/category', '/user/', '/profile/',
            '/login', '/register', '/cart', '/checkout',
            '/help', '/about', '/contact', '/terms',
            '.jpg', '.png', '.gif', '.pdf', '.css', '.js'
        ]
        
        url_lower = url.lower()
        
        # Check if URL contains product indicators
        has_product_indicator = any(indicator in url_lower for indicator in product_indicators)
        
        # Check if URL should be excluded
        should_exclude = any(pattern in url_lower for pattern in exclude_patterns)
        
        return has_product_indicator and not should_exclude
        
    async def get_page_html(self) -> str:
        """Get the current page's HTML content."""
        try:
            return await self.page.content()
        except Exception as e:
            logger.error(f"Error getting page HTML: {e}")
            return ""
            
    async def get_page_title(self) -> str:
        """Get the current page's title."""
        try:
            return await self.page.title()
        except Exception as e:
            logger.error(f"Error getting page title: {e}")
            return ""
