"""
AI-powered data extraction module using OpenAI GPT API.
"""
import json
import logging
import re
from typing import Dict, Any, Optional
import openai
from config import Config

logger = logging.getLogger(__name__)

class AIExtractor:
    """Extracts structured product data using OpenAI GPT API."""
    
    def __init__(self):
        # Configure OpenAI client for OpenRouter
        openai.api_key = Config.OPENROUTER_API_KEY
        openai.api_base = Config.OPENAI_API_BASE
        
        self.extraction_prompt = self._build_extraction_prompt()
        
    def _build_extraction_prompt(self) -> str:
        """Build the prompt for product data extraction."""
        return """
You are a data extraction specialist. Extract product information from the provided HTML/text content.

IMPORTANT INSTRUCTIONS:
1. Return ONLY valid JSON with no additional text, explanations, or markdown formatting
2. Extract exactly these fields: name, price, description, image_url
3. If a field cannot be found, use an empty string ""
4. For price, extract only the numeric value (remove currency symbols)
5. For description, provide a concise summary (max 200 characters)
6. For image_url, provide the full URL to the main product image

REQUIRED JSON FORMAT:
{
    "name": "Product name here",
    "price": "Numeric price only",
    "description": "Brief product description",
    "image_url": "Full image URL",
    "condition": "Product condition new, refurblished etc",
    "vendor": "The name of the vendor",
    "warranty": "Warranty period if available",
    "delivery": "The delivery options and destinations if available",
    "contact": "Vendor contact info, email, whatsapp number, etc",
    "location": "The location of the vendor, if available",
    "market_price": "The market price of the product, if available"
}

CONTENT TO EXTRACT FROM:
"""

    async def extract_product_data(self, html_content: str, fallback_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Extract product data from HTML content using AI.
        
        Args:
            html_content: Cleaned HTML content
            fallback_data: Fallback data if AI extraction fails
            
        Returns:
            Dictionary with extracted product data
        """
        try:
            # Prepare content for AI processing
            processed_content = self._prepare_content_for_ai(html_content)
            
            if not processed_content.strip():
                logger.warning("No content to process for AI extraction")
                return fallback_data or self._get_empty_product_data()
                
            # Make API call to OpenAI
            response = await self._call_openai_api(processed_content)
            
            if response:
                # Parse and validate the response
                extracted_data = self._parse_ai_response(response)
                
                # Validate and clean the extracted data
                validated_data = self._validate_and_clean_data(extracted_data)
                
                # Merge with fallback data if needed
                if fallback_data:
                    validated_data = self._merge_with_fallback(validated_data, fallback_data)
                    
                logger.info("AI extraction completed successfully")
                return validated_data
            else:
                logger.warning("AI extraction failed, using fallback data")
                return fallback_data or self._get_empty_product_data()
                
        except Exception as e:
            logger.error(f"Error in AI extraction: {e}")
            return fallback_data or self._get_empty_product_data()
            
    def _prepare_content_for_ai(self, html_content: str) -> str:
        """Prepare content for AI processing by limiting size and focusing on relevant parts."""
        if not html_content:
            return ""
            
        # Limit content size to avoid token limits
        max_chars = 8000  # Conservative limit for GPT-4
        
        if len(html_content) <= max_chars:
            return html_content
            
        # Try to extract the most relevant parts
        relevant_patterns = [
            r'<h1[^>]*>.*?</h1>',
            r'<h2[^>]*>.*?</h2>',
            r'class="[^"]*price[^"]*"[^>]*>.*?</[^>]+>',
            r'class="[^"]*title[^"]*"[^>]*>.*?</[^>]+>',
            r'class="[^"]*description[^"]*"[^>]*>.*?</[^>]+>',
            r'class="[^"]*name[^"]*"[^>]*>.*?</[^>]+>',
            r'itemprop="[^"]*"[^>]*>.*?</[^>]+>',
            r'<img[^>]*src="[^"]*"[^>]*>',
        ]
        
        relevant_content = []
        for pattern in relevant_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            relevant_content.extend(matches)
            
        # If we found relevant content, use it
        if relevant_content:
            content = ' '.join(relevant_content)
            if len(content) <= max_chars:
                return content
                
        # Otherwise, truncate the original content
        return html_content[:max_chars] + "..."
        
    async def _call_openai_api(self, content: str) -> Optional[str]:
        """Make API call to OpenAI through OpenRouter."""
        try:
            full_prompt = self.extraction_prompt + "\n\n" + content
            
            response = openai.ChatCompletion.create(
                model=Config.MODEL_NAME,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a precise data extraction assistant. Return only valid JSON."
                    },
                    {
                        "role": "user",
                        "content": full_prompt
                    }
                ],
                max_tokens=500,
                temperature=0.1,  # Low temperature for consistent extraction
                timeout=30
            )
            
            if response.choices and response.choices[0].message:
                return response.choices[0].message.content.strip()
            else:
                logger.error("No response from OpenAI API")
                return None
                
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            return None
            
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response and extract JSON data."""
        try:
            # Clean the response - remove markdown formatting if present
            cleaned_response = response.strip()
            
            # Remove markdown code blocks if present
            if cleaned_response.startswith('```'):
                lines = cleaned_response.split('\n')
                cleaned_response = '\n'.join(lines[1:-1])
                
            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # Try to parse the entire response as JSON
                return json.loads(cleaned_response)
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            logger.debug(f"AI response was: {response}")
            return {}
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
            return {}
            
    def _validate_and_clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean extracted data."""
        cleaned_data = self._get_empty_product_data()
        
        if not isinstance(data, dict):
            return cleaned_data
            
        # Clean name
        if 'name' in data and data['name']:
            cleaned_data['name'] = str(data['name']).strip()[:200]
            
        # Clean price
        if 'price' in data and data['price']:
            price_str = str(data['price']).strip()
            # Extract numeric value from price
            price_match = re.search(r'[\d,]+(?:\.\d{2})?', price_str)
            if price_match:
                cleaned_data['price'] = price_match.group().replace(',', '')
                
        # Clean description
        if 'description' in data and data['description']:
            desc = str(data['description']).strip()
            # Remove excessive whitespace and limit length
            desc = re.sub(r'\s+', ' ', desc)
            cleaned_data['description'] = desc[:500]
            
        # Clean image URL
        if 'image_url' in data and data['image_url']:
            img_url = str(data['image_url']).strip()
            # Basic URL validation
            if img_url.startswith(('http://', 'https://', '//')):
                cleaned_data['image_url'] = img_url
            elif img_url.startswith('/'):
                # Relative URL - will be handled by the calling code
                cleaned_data['image_url'] = img_url
                
        return cleaned_data
        
    def _merge_with_fallback(self, ai_data: Dict[str, Any], fallback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Merge AI extracted data with fallback data."""
        merged_data = fallback_data.copy()
        
        # Use AI data if it's better than fallback
        for key in ['name', 'price', 'description', 'image_url']:
            if ai_data.get(key) and (not merged_data.get(key) or len(ai_data[key]) > len(merged_data.get(key, ''))):
                merged_data[key] = ai_data[key]
                
        return merged_data
        
    def _get_empty_product_data(self) -> Dict[str, Any]:
        """Return empty product data structure."""
        return {
            'name': '',
            'price': '',
            'description': '',
            'image_url': ''
        }
        
    def validate_extraction_quality(self, data: Dict[str, Any]) -> float:
        """
        Validate the quality of extracted data.
        
        Returns:
            Quality score between 0.0 and 1.0
        """
        score = 0.0
        total_fields = 4
        
        # Check each field
        if data.get('name') and len(data['name']) > 3:
            score += 0.4  # Name is most important
            
        if data.get('price') and re.match(r'^\d+(?:\.\d{2})?$', data['price']):
            score += 0.3  # Price is very important
            
        if data.get('description') and len(data['description']) > 10:
            score += 0.2  # Description is helpful
            
        if data.get('image_url') and data['image_url'].startswith(('http', '//')):
            score += 0.1  # Image is nice to have
            
        return min(score, 1.0)
