"""
Configuration settings for the AI Scraper tool.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for scraper settings."""
    
    # API Configuration
    OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY', '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')
    OPENAI_API_BASE = "https://openrouter.ai/api/v1"
    MODEL_NAME = "openai/gpt-3.5-turbo"
    
    # Scraping Configuration
    START_URL = os.getenv('START_URL', 'https://jiji.co.tz/mobile-phones')
    MAX_PRODUCTS = int(os.getenv('MAX_PRODUCTS', '10'))
    SCROLL_ATTEMPTS = int(os.getenv('SCROLL_ATTEMPTS', '10'))
    RETRY_ATTEMPTS = int(os.getenv('RETRY_ATTEMPTS', '3'))
    
    # Browser Configuration
    HEADLESS = os.getenv('HEADLESS', 'true').lower() == 'true'
    BROWSER_TIMEOUT = int(os.getenv('BROWSER_TIMEOUT', '30000'))  # milliseconds
    PAGE_LOAD_TIMEOUT = int(os.getenv('PAGE_LOAD_TIMEOUT', '60000'))  # milliseconds
    
    # Output Configuration
    OUTPUT_CSV_PATH = os.getenv('OUTPUT_CSV_PATH', 'data/output.csv')
    RAW_HTML_DIR = os.getenv('RAW_HTML_DIR', 'data/raw_html')
    IMAGES_DIR = os.getenv('IMAGES_DIR', 'data/images')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/scrape.log')
    
    # Debug Configuration
    DEBUG = os.getenv('DEBUG', 'true').lower() == 'true'
    SAVE_RAW_HTML = os.getenv('SAVE_RAW_HTML', 'true').lower() == 'true'
    
    # Request Configuration
    REQUEST_DELAY = float(os.getenv('REQUEST_DELAY', '1.0'))  # seconds between requests
    USER_AGENT = os.getenv('USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    @classmethod
    def validate(cls):
        """Validate configuration settings."""
        if not cls.OPENROUTER_API_KEY:
            raise ValueError("OPENROUTER_API_KEY is required")
        
        if cls.MAX_PRODUCTS <= 0:
            raise ValueError("MAX_PRODUCTS must be greater than 0")
        
        if not cls.START_URL:
            raise ValueError("START_URL is required")
        
        return True

# Validate configuration on import
Config.validate()
