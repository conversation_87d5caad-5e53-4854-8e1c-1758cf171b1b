# AI Scraper

A powerful Python CLI tool for scraping product listings from e-commerce websites using AI-powered data extraction.

## Features

- **Browser Automation**: Uses Playwright for reliable web scraping with headless Chromium
- **AI-Powered Extraction**: Leverages OpenAI GPT API to extract structured data from HTML
- **HTML Cleaning**: BeautifulSoup-based cleaning to remove scripts, ads, and irrelevant content
- **Image Downloads**: Automatically downloads and processes product images
- **Robust Error Handling**: Comprehensive retry logic and fallback mechanisms
- **Configurable**: Easy configuration through environment variables
- **Production Ready**: Clean modular architecture with logging and progress tracking

## Project Structure

```
ai_scraper/
├── main.py                 # Main CLI application
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── .env                   # Environment variables
├── README.md              # This file
├── test_basic.py          # Basic functionality tests
├── scraping/              # Core scraping modules
│   ├── __init__.py
│   ├── browser.py         # Playwright browser automation
│   ├── cleaner.py         # HTML cleaning with BeautifulSoup
│   ├── extractor.py       # AI-powered data extraction
│   └── utils.py           # Utilities (file ops, image download, logging)
├── data/                  # Output directory
│   ├── raw_html/          # Raw HTML files (optional)
│   ├── images/            # Downloaded product images
│   └── output.csv         # Scraped data in CSV format
└── logs/
    └── scrape.log         # Application logs
```

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Playwright browsers**:
   ```bash
   playwright install chromium
   ```

4. **Configure environment variables** (optional):
   Edit the `.env` file to customize settings:
   ```env
   OPENROUTER_API_KEY=your_api_key_here
   START_URL=https://jiji.co.tz/mobile-phones
   MAX_PRODUCTS=10
   DEBUG=true
   ```

## Usage

### Basic Usage

```bash
# Scrape with default settings (10 products from Jiji mobile phones)
python main.py

# Scrape a specific URL
python main.py --url "https://jiji.co.tz/cars"

# Scrape more products
python main.py --max-products 20

# Enable debug mode
python main.py --debug
```

### Command Line Options

- `--url`: Start URL to scrape (default: from config)
- `--max-products`: Maximum number of products to scrape
- `--output`: Output CSV file path
- `--debug`: Enable debug logging
- `--headless`: Run browser in headless mode (default: True)

### Configuration

The tool can be configured through environment variables in the `.env` file:

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENROUTER_API_KEY` | OpenRouter API key for GPT access | Required |
| `START_URL` | Default URL to scrape | `https://jiji.co.tz/mobile-phones` |
| `MAX_PRODUCTS` | Default max products to scrape | `10` |
| `HEADLESS` | Run browser in headless mode | `true` |
| `DEBUG` | Enable debug logging | `true` |
| `SCROLL_ATTEMPTS` | Number of scroll attempts to load more products | `3` |
| `RETRY_ATTEMPTS` | Number of retry attempts for failed requests | `3` |
| `REQUEST_DELAY` | Delay between requests (seconds) | `1.0` |

## Output

The scraper generates:

1. **CSV File** (`data/output.csv`): Structured product data with columns:
   - `name`: Product name
   - `price`: Product price (numeric)
   - `description`: Product description
   - `image_url`: Original image URL
   - `source_url`: Source product page URL
   - `scraped_at`: Timestamp of scraping

2. **Images** (`data/images/`): Downloaded product images with clean filenames

3. **Raw HTML** (`data/raw_html/`): Optional raw HTML files for debugging

4. **Logs** (`logs/scrape.log`): Detailed application logs

## How It Works

1. **Navigation**: Uses Playwright to navigate to the target URL
2. **Product Discovery**: Scrolls and extracts product listing URLs
3. **Data Extraction**: For each product:
   - Navigates to the product page
   - Extracts and cleans HTML content
   - Uses AI (GPT) to extract structured data
   - Downloads the main product image
   - Validates and saves the data
4. **Output**: Saves all data to CSV and images to local files

## Testing

Run the basic functionality test:

```bash
python test_basic.py
```

This validates:
- File structure
- Module imports
- HTML cleaning functionality
- File management operations

## Error Handling

The scraper includes comprehensive error handling:

- **Network Issues**: Automatic retries with exponential backoff
- **Missing Data**: Fallback extraction methods
- **Invalid URLs**: URL validation and filtering
- **API Failures**: Graceful degradation when AI extraction fails
- **File Operations**: Safe file handling with proper error reporting

## Supported Websites

The scraper is designed to work with various e-commerce sites, with specific optimizations for:

- Jiji Tanzania (jiji.co.tz)
- General e-commerce sites with standard product listing patterns

The AI-powered extraction makes it adaptable to different website structures.

## Requirements

- Python 3.8+
- Internet connection
- OpenRouter API key (for AI extraction)
- Sufficient disk space for images and data

## Limitations

- Respects website rate limits (configurable delays)
- Requires valid OpenRouter API key for AI extraction
- Some websites may have anti-bot measures
- Image download depends on image URL accessibility

## Contributing

The code is modular and well-documented. Key areas for enhancement:

- Additional website-specific optimizations
- Enhanced image processing
- Database storage options
- API endpoint for programmatic access

## License

This project is provided as-is for educational and research purposes.
