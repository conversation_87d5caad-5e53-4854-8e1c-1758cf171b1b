#!/usr/bin/env python3
"""
Basic test script to validate the scraper structure and imports.
"""
import sys
import os

def test_imports():
    """Test if all modules can be imported."""
    print("Testing imports...")
    
    try:
        from config import Config
        print("✓ Config imported successfully")
        print(f"  - Start URL: {Config.START_URL}")
        print(f"  - Max products: {Config.MAX_PRODUCTS}")
        print(f"  - Output path: {Config.OUTPUT_CSV_PATH}")
    except Exception as e:
        print(f"✗ Config import failed: {e}")
        return False
    
    try:
        from scraping.cleaner import HTMLCleaner
        print("✓ HTMLCleaner imported successfully")
    except Exception as e:
        print(f"✗ HTMLCleaner import failed: {e}")
        return False
    
    try:
        from scraping.utils import FileManager, DataValidator
        print("✓ Utils imported successfully")
    except Exception as e:
        print(f"✗ Utils import failed: {e}")
        return False
    
    return True

def test_file_structure():
    """Test if all required files and directories exist."""
    print("\nTesting file structure...")
    
    required_files = [
        'main.py',
        'config.py',
        'requirements.txt',
        '.env',
        'scraping/__init__.py',
        'scraping/browser.py',
        'scraping/cleaner.py',
        'scraping/extractor.py',
        'scraping/utils.py'
    ]
    
    required_dirs = [
        'data',
        'data/raw_html',
        'data/images',
        'logs',
        'scraping'
    ]
    
    all_good = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} missing")
            all_good = False
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"✓ {dir_path}/")
        else:
            print(f"✗ {dir_path}/ missing")
            all_good = False
    
    return all_good

def test_html_cleaner():
    """Test HTML cleaner functionality."""
    print("\nTesting HTML cleaner...")
    
    try:
        from scraping.cleaner import HTMLCleaner
        
        cleaner = HTMLCleaner()
        
        # Test HTML content
        test_html = """
        <html>
        <head><title>Test Product</title></head>
        <body>
            <script>alert('test');</script>
            <div class="product-title">iPhone 14 Pro</div>
            <div class="price">$999</div>
            <div class="description">Latest iPhone with amazing features</div>
            <img src="iphone.jpg" alt="iPhone 14 Pro">
            <div class="advertisement">Buy now!</div>
        </body>
        </html>
        """
        
        cleaned = cleaner.clean_html(test_html)
        
        if cleaned and len(cleaned) > 0:
            print("✓ HTML cleaning works")
            print(f"  Original length: {len(test_html)}")
            print(f"  Cleaned length: {len(cleaned)}")
            
            # Test product info extraction
            product_info = cleaner.extract_product_info(test_html)
            print(f"  Extracted info: {product_info}")
            
            return True
        else:
            print("✗ HTML cleaning failed")
            return False
            
    except Exception as e:
        print(f"✗ HTML cleaner test failed: {e}")
        return False

def test_file_manager():
    """Test file manager functionality."""
    print("\nTesting file manager...")
    
    try:
        from scraping.utils import FileManager
        
        fm = FileManager()
        
        # Test filename generation
        filename = fm.generate_filename("iPhone 14 Pro Max", "Apple")
        print(f"✓ Generated filename: {filename}")
        
        # Test CSV structure (without actually saving)
        test_data = [
            {
                'name': 'Test Product',
                'price': '100',
                'description': 'Test description',
                'image_url': 'http://example.com/image.jpg',
                'source_url': 'http://example.com/product',
                'scraped_at': '2024-01-01T00:00:00'
            }
        ]
        
        print("✓ File manager initialized successfully")
        return True
        
    except Exception as e:
        print(f"✗ File manager test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("AI Scraper - Basic Functionality Test")
    print("=" * 40)
    
    tests = [
        test_file_structure,
        test_imports,
        test_html_cleaner,
        test_file_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All basic tests passed! The scraper structure is ready.")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Install Playwright browsers: playwright install chromium")
        print("3. Run the scraper: python main.py --max-products 2")
    else:
        print("✗ Some tests failed. Please check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
